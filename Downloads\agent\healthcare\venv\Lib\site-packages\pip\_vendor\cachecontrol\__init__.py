# SPDX-FileCopyrightText: 2015 <PERSON>
#
# SPDX-License-Identifier: Apache-2.0

"""CacheControl import Interface.

Make it easy to import from cachecontrol without long namespaces.
"""
__author__ = "<PERSON>"
__email__ = "<EMAIL>"
__version__ = "0.12.11"

from .wrapper import CacheControl
from .adapter import CacheControlAdapter
from .controller import CacheController

import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())
